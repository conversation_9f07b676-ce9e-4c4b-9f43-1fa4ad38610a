import { Injectable } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { AdministrationService } from '../administration.service';
import { AuthNoticeService } from '@core/auth';
import { AppConstants } from '@shared/constants';

@Injectable()
export abstract class TagBaseComponent extends SflBaseComponent {
  duplicateError: string = '';

  constructor(
    protected readonly administrationService: AdministrationService,
    protected readonly authNoticeService: AuthNoticeService
  ) {
    super();
  }

  // Clear duplicate error message
  clearDuplicateError(): void {
    this.duplicateError = '';
  }

  // Tag Category validation methods
  async isDuplicateTagCategory(categoryName: string, excludeId?: number): Promise<boolean> {
    return new Promise((resolve) => {
      const currentName = categoryName?.replace(/ /g, '-')?.toLowerCase()?.trim();
      
      if (!currentName) {
        resolve(false);
        return;
      }

      // Load data if not available
      if (!this.administrationService.tagCategories?.extended_fields?.tagCategory) {
        this.subscriptionManager.add(
          this.administrationService.getTagCategories('TagCategoryManagement').subscribe(
            (res) => {
              if (res?.data?.global_details) {
                const globalDetail = res?.data?.global_details;
                if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
                  this.administrationService.setTagCategories(globalDetail[0].global_detail);
                  resolve(this.checkCategoryDuplicate(currentName, excludeId));
                } else {
                  resolve(false);
                }
              } else {
                resolve(false);
              }
            },
            () => resolve(false)
          )
        );
      } else {
        resolve(this.checkCategoryDuplicate(currentName, excludeId));
      }
    });
  }

  private checkCategoryDuplicate(currentName: string, excludeId?: number): boolean {
    if (!this.administrationService.tagCategories?.extended_fields?.tagCategory) {
      return false;
    }

    let existingCategories = this.administrationService.tagCategories.extended_fields.tagCategory;
    
    // Exclude current category if editing
    if (excludeId) {
      existingCategories = existingCategories.filter((category) => category.id !== excludeId);
    }
    
    const duplicate = existingCategories.find(
      (category) => category.name?.toLowerCase()?.trim() === currentName
    );
    return !!duplicate;
  }

  // Tag Sub Category validation methods
  async isDuplicateTagSubCategory(subCategoryName: string, parentCategoryId: string, excludeId?: number): Promise<boolean> {
    return new Promise((resolve) => {
      const currentName = subCategoryName?.replace(/ /g, '-')?.toLowerCase()?.trim();
      
      if (!currentName || !parentCategoryId) {
        resolve(false);
        return;
      }

      // Load data if not available
      if (!this.administrationService.subCategories?.extended_fields?.subCategory) {
        this.subscriptionManager.add(
          this.administrationService.getTagSubCategories('SubCategoryManagement').subscribe(
            (res) => {
              if (res?.data?.global_details) {
                const globalDetail = res?.data?.global_details;
                if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
                  this.administrationService.setTagSubCategories(globalDetail[0].global_detail);
                  resolve(this.checkSubCategoryDuplicate(currentName, parentCategoryId, excludeId));
                } else {
                  resolve(false);
                }
              } else {
                resolve(false);
              }
            },
            () => resolve(false)
          )
        );
      } else {
        resolve(this.checkSubCategoryDuplicate(currentName, parentCategoryId, excludeId));
      }
    });
  }

  private checkSubCategoryDuplicate(currentName: string, parentCategoryId: string, excludeId?: number): boolean {
    if (!this.administrationService.subCategories?.extended_fields?.subCategory) {
      return false;
    }

    const existingSubCategories = this.administrationService.subCategories.extended_fields.subCategory;
    
    // Filter sub categories by parent category and exclude current one if editing
    let filteredSubCategories = existingSubCategories.filter(
      (subCategory) => subCategory.parentCategoryId === parentCategoryId
    );
    
    if (excludeId) {
      filteredSubCategories = filteredSubCategories.filter(
        (subCategory) => subCategory.id !== excludeId
      );
    }
    
    const duplicate = filteredSubCategories.find(
      (subCategory) => subCategory.name?.toLowerCase()?.trim() === currentName
    );
    return !!duplicate;
  }

  // Tag validation methods
  async isDuplicateTag(tagName: string, tagCategory: string, subTagCategory?: string, excludeId?: number): Promise<boolean> {
    return new Promise((resolve) => {
      const currentName = tagName?.replace(/ /g, '-')?.toLowerCase()?.trim();
      
      if (!currentName || !tagCategory) {
        resolve(false);
        return;
      }

      // Load data if not available
      if (!this.administrationService.tags?.extended_fields?.tags) {
        this.subscriptionManager.add(
          this.administrationService.getTags('TagManagement').subscribe(
            (res) => {
              if (res?.data?.global_details) {
                const globalDetail = res?.data?.global_details;
                if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagManagement') {
                  this.administrationService.setTags(globalDetail[0].global_detail);
                  resolve(this.checkTagDuplicate(currentName, tagCategory, subTagCategory, excludeId));
                } else {
                  resolve(false);
                }
              } else {
                resolve(false);
              }
            },
            () => resolve(false)
          )
        );
      } else {
        resolve(this.checkTagDuplicate(currentName, tagCategory, subTagCategory, excludeId));
      }
    });
  }

  private checkTagDuplicate(currentName: string, tagCategory: string, subTagCategory?: string, excludeId?: number): boolean {
    if (!this.administrationService.tags?.extended_fields?.tags) {
      return false;
    }

    const existingTags = this.administrationService.tags.extended_fields.tags;
    
    // Filter tags by category and sub category, exclude current one if editing
    let filteredTags = existingTags.filter((tag) => {
      const matchesCategory = tag.tagCategory === tagCategory;
      const matchesSubCategory = subTagCategory ? tag.subTagCategory === subTagCategory : !tag.subTagCategory;
      return matchesCategory && matchesSubCategory;
    });
    
    if (excludeId) {
      filteredTags = filteredTags.filter((tag) => tag.id !== excludeId);
    }
    
    const duplicate = filteredTags.find(
      (tag) => tag.name?.toLowerCase()?.trim() === currentName
    );
    return !!duplicate;
  }

  // Data loading helper methods
  loadTagCategoriesIfNeeded(): void {
    if (!this.administrationService.tagCategories?.extended_fields?.tagCategory) {
      this.subscriptionManager.add(
        this.administrationService.getTagCategories('TagCategoryManagement').subscribe(
          (res) => {
            if (res?.data?.global_details) {
              const globalDetail = res?.data?.global_details;
              if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
                this.administrationService.setTagCategories(globalDetail[0].global_detail);
              }
            }
          }
        )
      );
    }
  }

  loadTagSubCategoriesIfNeeded(): void {
    if (!this.administrationService.subCategories?.extended_fields?.subCategory) {
      this.subscriptionManager.add(
        this.administrationService.getTagSubCategories('SubCategoryManagement').subscribe(
          (res) => {
            if (res?.data?.global_details) {
              const globalDetail = res?.data?.global_details;
              if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
                this.administrationService.setTagSubCategories(globalDetail[0].global_detail);
              }
            }
          }
        )
      );
    }
  }

  loadTagsIfNeeded(): void {
    if (!this.administrationService.tags?.extended_fields?.tags) {
      this.subscriptionManager.add(
        this.administrationService.getTags('TagManagement').subscribe(
          (res) => {
            if (res?.data?.global_details) {
              const globalDetail = res?.data?.global_details;
              if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagManagement') {
                this.administrationService.setTags(globalDetail[0].global_detail);
              }
            }
          }
        )
      );
    }
  }

  // Common validation methods for each component type
  async checkTagCategoryDuplicateOnBlur(categoryName: string, excludeId?: number): Promise<void> {
    this.duplicateError = '';
    if (categoryName?.trim()) {
      if (await this.isDuplicateTagCategory(categoryName, excludeId)) {
        this.duplicateError = AppConstants.duplicateTagCategory;
      }
    }
  }

  async checkTagSubCategoryDuplicateOnBlur(subCategoryName: string, parentCategoryId: string, excludeId?: number): Promise<void> {
    this.duplicateError = '';
    if (subCategoryName?.trim() && parentCategoryId) {
      if (await this.isDuplicateTagSubCategory(subCategoryName, parentCategoryId, excludeId)) {
        this.duplicateError = AppConstants.duplicateTagSubCategory;
      }
    }
  }

  async checkTagDuplicateOnBlur(tagName: string, tagCategory: string, subTagCategory?: string, excludeId?: number): Promise<void> {
    this.duplicateError = '';
    if (tagName?.trim() && tagCategory) {
      if (await this.isDuplicateTag(tagName, tagCategory, subTagCategory, excludeId)) {
        this.duplicateError = AppConstants.duplicateTag;
      }
    }
  }
}
